'use client';

import { useState, useEffect } from 'react';
import { <PERSON>fresh<PERSON>w, Trash2, <PERSON><PERSON>, Eye, AlertCircle, CheckCircle, Info, AlertTriangle } from 'lucide-react';
import { toast } from 'sonner';

interface SniperLog {
  id: number;
  sessionId: string;
  userAddress: string;
  chainId: string;
  logLevel: 'INFO' | 'DEBUG' | 'ERROR' | 'SUCCESS' | 'WARNING';
  logType: string;
  message: string;
  transactionHash?: string;
  tokenAddress?: string;
  watchedAddress?: string;
  metadata?: any;
  createdAt: string;
}

interface SniperLogsProps {
  userAddress: string;
  sessionId: string;
  chainId: string;
  isVisible: boolean;
}

export default function SniperLogs({ userAddress, sessionId, chainId, isVisible }: SniperLogsProps) {
  const [logs, setLogs] = useState<SniperLog[]>([]);
  const [loading, setLoading] = useState(false);
  const [filter, setFilter] = useState({
    logLevel: '',
    logType: '',
    sessionOnly: true
  });

  const fetchLogs = async () => {
    if (!isVisible) return;
    
    setLoading(true);
    try {
      const url = new URL('/api/sniper/logs', window.location.origin);
      url.searchParams.set('userAddress', userAddress);
      url.searchParams.set('chainId', chainId);
      url.searchParams.set('limit', '200');
      
      if (filter.sessionOnly) {
        url.searchParams.set('sessionId', sessionId);
      }
      
      if (filter.logLevel) {
        url.searchParams.set('logLevel', filter.logLevel);
      }
      
      if (filter.logType) {
        url.searchParams.set('logType', filter.logType);
      }

      const response = await fetch(url.toString());
      const data = await response.json();

      if (data.success) {
        setLogs(data.logs);
      } else {
        toast.error('Failed to fetch logs');
      }
    } catch (error) {
      console.error('Error fetching logs:', error);
      toast.error('Failed to fetch logs');
    } finally {
      setLoading(false);
    }
  };

  const clearLogs = async () => {
    try {
      const url = new URL('/api/sniper/logs', window.location.origin);
      url.searchParams.set('userAddress', userAddress);
      
      if (filter.sessionOnly) {
        url.searchParams.set('sessionId', sessionId);
      }

      const response = await fetch(url.toString(), {
        method: 'DELETE'
      });

      const data = await response.json();

      if (data.success) {
        toast.success(data.message);
        setLogs([]);
      } else {
        toast.error('Failed to clear logs');
      }
    } catch (error) {
      console.error('Error clearing logs:', error);
      toast.error('Failed to clear logs');
    }
  };

  useEffect(() => {
    if (isVisible) {
      fetchLogs();
      // Auto-refresh every 5 seconds when visible
      const interval = setInterval(fetchLogs, 5000);
      return () => clearInterval(interval);
    }
  }, [isVisible, filter, sessionId]);

  const getLogLevelIcon = (level: string) => {
    switch (level) {
      case 'SUCCESS':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'ERROR':
        return <AlertCircle className="h-4 w-4 text-red-500" />;
      case 'WARNING':
        return <AlertTriangle className="h-4 w-4 text-yellow-500" />;
      case 'INFO':
        return <Info className="h-4 w-4 text-blue-500" />;
      case 'DEBUG':
        return <Eye className="h-4 w-4 text-gray-500" />;
      default:
        return <Info className="h-4 w-4 text-gray-500" />;
    }
  };

  const getLogLevelColor = (level: string) => {
    switch (level) {
      case 'SUCCESS':
        return 'text-green-600 bg-green-50';
      case 'ERROR':
        return 'text-red-600 bg-red-50';
      case 'WARNING':
        return 'text-yellow-600 bg-yellow-50';
      case 'INFO':
        return 'text-blue-600 bg-blue-50';
      case 'DEBUG':
        return 'text-gray-600 bg-gray-50';
      default:
        return 'text-gray-600 bg-gray-50';
    }
  };

  const formatTimestamp = (timestamp: string) => {
    return new Date(timestamp).toLocaleString();
  };

  const formatAddress = (addr: string) => {
    return `${addr.slice(0, 6)}...${addr.slice(-4)}`;
  };

  if (!isVisible) return null;

  return (
    <div className="bg-white rounded-lg shadow-sm border p-6">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold">Sniper Logs</h3>
        <div className="flex items-center gap-2">
          <button
            onClick={fetchLogs}
            disabled={loading}
            className="flex items-center gap-2 px-3 py-1 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50"
          >
            <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </button>
          <button
            onClick={clearLogs}
            className="flex items-center gap-2 px-3 py-1 bg-red-500 text-white rounded hover:bg-red-600"
          >
            <Trash2 className="h-4 w-4" />
            Clear
          </button>
        </div>
      </div>

      {/* Filters */}
      <div className="flex items-center gap-4 mb-4 p-3 bg-gray-50 rounded">
        <div className="flex items-center gap-2">
          <Filter className="h-4 w-4" />
          <span className="text-sm font-medium">Filters:</span>
        </div>
        
        <select
          value={filter.logLevel}
          onChange={(e) => setFilter(prev => ({ ...prev, logLevel: e.target.value }))}
          className="px-2 py-1 border rounded text-sm"
        >
          <option value="">All Levels</option>
          <option value="SUCCESS">Success</option>
          <option value="ERROR">Error</option>
          <option value="WARNING">Warning</option>
          <option value="INFO">Info</option>
          <option value="DEBUG">Debug</option>
        </select>

        <select
          value={filter.logType}
          onChange={(e) => setFilter(prev => ({ ...prev, logType: e.target.value }))}
          className="px-2 py-1 border rounded text-sm"
        >
          <option value="">All Types</option>
          <option value="TOKEN_CREATION_DETECTED">Token Creation</option>
          <option value="AUTO_BUY_TRIGGER">Auto Buy</option>
          <option value="TRANSACTION_SCAN">Transaction Scan</option>
          <option value="FACTORY_CHECK">Factory Check</option>
          <option value="TRANSFER_EVENT_CHECK">Transfer Events</option>
        </select>

        <label className="flex items-center gap-2 text-sm">
          <input
            type="checkbox"
            checked={filter.sessionOnly}
            onChange={(e) => setFilter(prev => ({ ...prev, sessionOnly: e.target.checked }))}
          />
          Current Session Only
        </label>
      </div>

      {/* Logs */}
      <div className="space-y-2 max-h-96 overflow-y-auto">
        {logs.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            {loading ? 'Loading logs...' : 'No logs found'}
          </div>
        ) : (
          logs.map((log) => (
            <div
              key={log.id}
              className={`p-3 rounded border-l-4 ${
                log.logLevel === 'SUCCESS' ? 'border-l-green-500' :
                log.logLevel === 'ERROR' ? 'border-l-red-500' :
                log.logLevel === 'WARNING' ? 'border-l-yellow-500' :
                log.logLevel === 'INFO' ? 'border-l-blue-500' :
                'border-l-gray-500'
              } ${getLogLevelColor(log.logLevel)}`}
            >
              <div className="flex items-start justify-between">
                <div className="flex items-start gap-2 flex-1">
                  {getLogLevelIcon(log.logLevel)}
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-1">
                      <span className="font-medium text-sm">{log.logType}</span>
                      <span className="text-xs text-gray-500">{formatTimestamp(log.createdAt)}</span>
                    </div>
                    <p className="text-sm">{log.message}</p>
                    
                    {/* Additional info */}
                    <div className="mt-2 text-xs text-gray-600 space-y-1">
                      {log.transactionHash && (
                        <div>TX: {formatAddress(log.transactionHash)}</div>
                      )}
                      {log.tokenAddress && (
                        <div>Token: {formatAddress(log.tokenAddress)}</div>
                      )}
                      {log.watchedAddress && (
                        <div>Watched: {formatAddress(log.watchedAddress)}</div>
                      )}
                      {log.metadata && (
                        <details className="mt-1">
                          <summary className="cursor-pointer text-gray-500">Metadata</summary>
                          <pre className="mt-1 p-2 bg-gray-100 rounded text-xs overflow-x-auto">
                            {JSON.stringify(log.metadata, null, 2)}
                          </pre>
                        </details>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ))
        )}
      </div>
    </div>
  );
}
