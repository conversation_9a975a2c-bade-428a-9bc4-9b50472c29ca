import { db } from '@/db/drizzle';
import { sniperLogs } from '@/db/schema';

export type LogLevel = 'INFO' | 'DEBUG' | 'ERROR' | 'SUCCESS' | 'WARNING';
export type LogType = 
  | 'MONITORING_START' 
  | 'MONITORING_STOP'
  | 'TRANSACTION_SCAN' 
  | 'TOKEN_CREATION_CHECK'
  | 'TOKEN_CREATION_DETECTED'
  | 'AUTO_BUY_TRIGGER'
  | 'AUTO_BUY_SUCCESS'
  | 'AUTO_BUY_ERROR'
  | 'FACTORY_CHECK'
  | 'TRANSFER_EVENT_CHECK'
  | 'CONFIG_UPDATE'
  | 'ERROR';

export interface SniperLogData {
  sessionId: string;
  userAddress: string;
  chainId: string;
  logLevel: LogLevel;
  logType: LogType;
  message: string;
  transactionHash?: string;
  tokenAddress?: string;
  watchedAddress?: string;
  metadata?: any;
}

/**
 * Log sniper activity to database
 */
export async function logSniperActivity(data: SniperLogData): Promise<void> {
  try {
    await db.insert(sniperLogs).values({
      sessionId: data.sessionId,
      userAddress: data.userAddress,
      chainId: data.chainId,
      logLevel: data.logLevel,
      logType: data.logType,
      message: data.message,
      transactionHash: data.transactionHash || null,
      tokenAddress: data.tokenAddress || null,
      watchedAddress: data.watchedAddress || null,
      metadata: data.metadata || null,
    });
  } catch (error) {
    // Fallback to console if database logging fails
    console.error('Failed to log sniper activity to database:', error);
    console.log('Sniper log:', data);
  }
}

/**
 * Create a logger instance for a specific session
 */
export class SniperLogger {
  private sessionId: string;
  private userAddress: string;
  private chainId: string;

  constructor(sessionId: string, userAddress: string, chainId: string) {
    this.sessionId = sessionId;
    this.userAddress = userAddress;
    this.chainId = chainId;
  }

  async log(logLevel: LogLevel, logType: LogType, message: string, options?: {
    transactionHash?: string;
    tokenAddress?: string;
    watchedAddress?: string;
    metadata?: any;
  }): Promise<void> {
    await logSniperActivity({
      sessionId: this.sessionId,
      userAddress: this.userAddress,
      chainId: this.chainId,
      logLevel,
      logType,
      message,
      ...options
    });
  }

  async info(logType: LogType, message: string, options?: {
    transactionHash?: string;
    tokenAddress?: string;
    watchedAddress?: string;
    metadata?: any;
  }): Promise<void> {
    await this.log('INFO', logType, message, options);
  }

  async debug(logType: LogType, message: string, options?: {
    transactionHash?: string;
    tokenAddress?: string;
    watchedAddress?: string;
    metadata?: any;
  }): Promise<void> {
    await this.log('DEBUG', logType, message, options);
  }

  async error(logType: LogType, message: string, options?: {
    transactionHash?: string;
    tokenAddress?: string;
    watchedAddress?: string;
    metadata?: any;
  }): Promise<void> {
    await this.log('ERROR', logType, message, options);
  }

  async success(logType: LogType, message: string, options?: {
    transactionHash?: string;
    tokenAddress?: string;
    watchedAddress?: string;
    metadata?: any;
  }): Promise<void> {
    await this.log('SUCCESS', logType, message, options);
  }

  async warning(logType: LogType, message: string, options?: {
    transactionHash?: string;
    tokenAddress?: string;
    watchedAddress?: string;
    metadata?: any;
  }): Promise<void> {
    await this.log('WARNING', logType, message, options);
  }
}
