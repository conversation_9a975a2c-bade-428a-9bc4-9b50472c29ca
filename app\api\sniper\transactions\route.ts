import { NextRequest } from 'next/server';
import { createPublicClient, http, formatUnits } from 'viem';
import { cronos } from 'viem/chains';
import { <PERSON><PERSON><PERSON><PERSON>ogger } from '@/lib/sniperLogger';
import { randomUUID } from 'crypto';

// Create a public client for Cronos chain
const publicClient = createPublicClient({
  chain: cronos,
  transport: http()
});

// Known factory contracts that create tokens
const TOKEN_FACTORY_CONTRACTS = [
  '******************************************', // The factory from your example
  // Add more factory contracts here as you discover them
];

// Function to detect if transaction creates a new token
async function analyzeTokenCreation(tx: any, receipt: any, logger?: SniperLogger, watchedAddress?: string) {
  const result = {
    isTokenCreation: false,
    createdTokenAddress: null as string | null,
    tokenName: null as string | null,
    tokenSymbol: null as string | null,
    logs: [] as any[]
  };

  if (logger) {
    await logger.debug('TOKEN_CREATION_CHECK', `Analyzing transaction: ${tx.hash}`, {
      transactionHash: tx.hash,
      watchedAddress,
      metadata: {
        transactionTo: tx.to?.toLowerCase(),
        knownFactories: TOKEN_FACTORY_CONTRACTS
      }
    });
  }

  // Check if transaction is to a known factory contract
  const isFactoryTransaction = TOKEN_FACTORY_CONTRACTS.includes(tx.to?.toLowerCase() || '');

  if (logger) {
    await logger.debug('FACTORY_CHECK', `Factory transaction check: ${isFactoryTransaction}`, {
      transactionHash: tx.hash,
      watchedAddress,
      metadata: {
        transactionTo: tx.to?.toLowerCase(),
        isFactoryTransaction,
        knownFactories: TOKEN_FACTORY_CONTRACTS
      }
    });
  }

  if (!isFactoryTransaction || !receipt?.logs) {
    if (logger) {
      await logger.debug('TOKEN_CREATION_CHECK', `Skipping token creation analysis - not factory transaction or no logs`, {
        transactionHash: tx.hash,
        watchedAddress,
        metadata: {
          isFactoryTransaction,
          hasLogs: !!receipt?.logs,
          logCount: receipt?.logs?.length || 0
        }
      });
    }
    return result;
  }

  if (logger) {
    await logger.debug('TRANSFER_EVENT_CHECK', `Analyzing ${receipt.logs.length} logs for Transfer events`, {
      transactionHash: tx.hash,
      watchedAddress,
      metadata: {
        logCount: receipt.logs.length
      }
    });
  }

  // Look for contract creation in logs and Transfer events from null address
  for (const log of receipt.logs) {
    // Transfer event signature: Transfer(address,address,uint256)
    const transferEventSignature = '0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef';

    if (log.topics && log.topics[0] === transferEventSignature) {
      // Check if it's a mint (from null address)
      const fromAddress = log.topics[1];
      const nullAddress = '0x0000000000000000000000000000000000000000000000000000000000000000';

      if (logger) {
        await logger.debug('TRANSFER_EVENT_CHECK', `Found Transfer event`, {
          transactionHash: tx.hash,
          tokenAddress: log.address,
          watchedAddress,
          metadata: {
            fromAddress,
            toAddress: log.topics[2],
            isFromNullAddress: fromAddress === nullAddress
          }
        });
      }

      if (fromAddress === nullAddress) {
        result.isTokenCreation = true;
        result.createdTokenAddress = log.address;
        result.logs.push(log);

        if (logger) {
          await logger.success('TOKEN_CREATION_DETECTED', `Token creation detected!`, {
            transactionHash: tx.hash,
            tokenAddress: log.address,
            watchedAddress,
            metadata: {
              transferLog: log
            }
          });
        }
        break;
      }
    }
  }

  return result;
}

interface Transaction {
  hash: string;
  from: string;
  to: string;
  value: string;
  timestamp: number;
  blockNumber: number;
  isTokenCreation?: boolean;
  createdTokenAddress?: string;
  tokenName?: string;
  tokenSymbol?: string;
  logs?: any[];
}

export async function GET(request: NextRequest): Promise<Response> {
  try {
    const { searchParams } = new URL(request.url);
    const address = searchParams.get('address');
    const chainId = searchParams.get('chainId') || '25';
    const limit = parseInt(searchParams.get('limit') || '5');
    const userAddress = searchParams.get('userAddress'); // Optional for logging
    const sessionId = searchParams.get('sessionId') || randomUUID(); // Create session if not provided

    if (!address) {
      return Response.json(
        { error: 'Address parameter is required' },
        { status: 400 }
      );
    }

    // Validate address format
    if (!address.match(/^0x[a-fA-F0-9]{40}$/)) {
      return Response.json(
        { error: 'Invalid address format' },
        { status: 400 }
      );
    }

    // Create logger if userAddress is provided
    let logger: SniperLogger | undefined;
    if (userAddress) {
      logger = new SniperLogger(sessionId, userAddress, chainId);
      await logger.info('TRANSACTION_SCAN', `Starting transaction scan for address: ${address}`, {
        watchedAddress: address,
        metadata: {
          limit,
          chainId
        }
      });
    }

    // Get the latest block number
    const latestBlock = await publicClient.getBlockNumber();

    if (logger) {
      await logger.debug('TRANSACTION_SCAN', `Latest block: ${latestBlock}`, {
        watchedAddress: address,
        metadata: {
          latestBlock: Number(latestBlock)
        }
      });
    }

    // We'll check the last 100 blocks for transactions (more reasonable for RPC limits)
    const blocksToCheck = 100n;
    const startBlock = latestBlock - blocksToCheck;

    const transactions: Transaction[] = [];
    let foundTransactions = 0;
    let blocksChecked = 0;

    // Check blocks in reverse order (newest first), but limit to avoid RPC timeouts
    for (let blockNum = latestBlock; blockNum > startBlock && foundTransactions < limit && blocksChecked < 50; blockNum--) {
      try {
        blocksChecked++;

        const block = await publicClient.getBlock({
          blockNumber: blockNum,
          includeTransactions: true
        });

        if (block.transactions && Array.isArray(block.transactions)) {
          for (const tx of block.transactions) {
            // Check if transaction involves the target address
            if (typeof tx === 'object' && tx !== null && 'from' in tx && 'to' in tx) {
              const txFrom = tx.from?.toLowerCase();
              const txTo = tx.to?.toLowerCase();
              const targetAddr = address.toLowerCase();

              if (txFrom === targetAddr || txTo === targetAddr) {
                if (logger) {
                  await logger.debug('TRANSACTION_SCAN', `Found transaction involving target address`, {
                    transactionHash: tx.hash,
                    watchedAddress: address,
                    metadata: {
                      from: txFrom,
                      to: txTo,
                      target: targetAddr,
                      value: tx.value?.toString() || '0'
                    }
                  });
                }

                // Get transaction receipt to analyze logs for token creation
                let tokenCreationInfo = {
                  isTokenCreation: false,
                  createdTokenAddress: null as string | null,
                  tokenName: null as string | null,
                  tokenSymbol: null as string | null,
                  logs: [] as any[]
                };

                try {
                  const receipt = await publicClient.getTransactionReceipt({
                    hash: tx.hash as `0x${string}`
                  });
                  tokenCreationInfo = await analyzeTokenCreation(tx, receipt, logger, address);
                } catch (receiptError) {
                  if (logger) {
                    await logger.error('ERROR', `Error getting receipt for transaction`, {
                      transactionHash: tx.hash,
                      watchedAddress: address,
                      metadata: {
                        error: receiptError instanceof Error ? receiptError.message : 'Unknown error'
                      }
                    });
                  }
                }

                transactions.push({
                  hash: tx.hash || '',
                  from: tx.from || '',
                  to: tx.to || '',
                  value: tx.value?.toString() || '0',
                  timestamp: Number(block.timestamp),
                  blockNumber: Number(blockNum),
                  isTokenCreation: tokenCreationInfo.isTokenCreation,
                  createdTokenAddress: tokenCreationInfo.createdTokenAddress || undefined,
                  tokenName: tokenCreationInfo.tokenName || undefined,
                  tokenSymbol: tokenCreationInfo.tokenSymbol || undefined,
                  logs: tokenCreationInfo.logs
                });

                foundTransactions++;
                if (foundTransactions >= limit) break;
              }
            }
          }
        }

        // Add a small delay to avoid overwhelming the RPC
        if (blocksChecked % 10 === 0) {
          await new Promise(resolve => setTimeout(resolve, 100));
        }

      } catch (blockError) {
        console.error(`Error fetching block ${blockNum}:`, blockError);
        // Continue with next block
        continue;
      }
    }

    // Sort transactions by block number (newest first)
    transactions.sort((a, b) => b.blockNumber - a.blockNumber);

    console.log(`Found ${transactions.length} transactions for address ${address}`);

    return Response.json({
      success: true,
      address,
      chainId,
      transactions: transactions.slice(0, limit),
      blocksChecked: blocksChecked,
      latestBlock: Number(latestBlock),
      totalFound: transactions.length
    });

  } catch (error) {
    console.error('Error fetching transactions:', error);
    
    // Return a more specific error message
    let errorMessage = 'Failed to fetch transactions';
    if (error instanceof Error) {
      errorMessage = error.message;
    }

    return Response.json(
      { 
        error: errorMessage,
        success: false 
      },
      { status: 500 }
    );
  }
}
